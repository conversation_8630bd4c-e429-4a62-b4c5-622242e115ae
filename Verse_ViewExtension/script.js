// Global variables
//TEST
// const API_SERVER = 'http://localhost:5001/api/v1';

//PROD
const API_SERVER = 'https://verse-view-265611274173.us-central1.run.app/api/v1';

// API Key management
const API_KEY = 's4lNDrBVgjmWhcnsGYDtydWiUx7wphLMCcfeNY_FwAM';

// Future functionality can be added here
// For example:
// - Random verse selection
// - Background image rotation
// - Verse of the day
// - Customization options

// Function to convert image URL to base64
async function imageUrlToBase64(url) {
    try {
        const response = await fetch(url);
        const blob = await response.blob();
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });
    } catch (error) {
        console.error('Error converting image to base64:', error);
        return null;
    }
}

// Function to hide loading message and show verse content
function hideLoadingMessage() {
    const loadingMessage = document.getElementById('loading-message');
    if (loadingMessage) {
        loadingMessage.style.display = 'none';
    }
}

// Function to fetch and display verse for a specific date
async function fetchAndDisplayVerse(dateString) {
    // Show loading message (it's already visible by default)

    // First check local storage
    const storageResult = await new Promise(resolve => {
        chrome.storage.local.get(['verseData', 'backgroundImageData'], resolve);
    });

    // If either verse data is missing or background image is missing, fetch fresh data
    if (!storageResult.verseData || !storageResult.backgroundImageData || storageResult.verseData.date !== dateString) {
        try {
            const response = await fetch(`${API_SERVER}/getverse?date=${dateString}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': API_KEY
                }
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();

            if (data && data.text && data.reference) {
                // Store the verse data
                await chrome.storage.local.set({
                    'verseData': {
                        date: dateString,
                        text: data.text,
                        reference: data.reference
                    }
                });

                // Display the verse
                document.getElementById('verse-text').textContent = data.text;
                document.getElementById('verse-reference').textContent = data.reference;

                // Hide loading message
                hideLoadingMessage();

                // Handle background image
                if (data.background_image) {
                    try {
                        // Convert image to base64 and store it
                        const base64Image = await imageUrlToBase64(data.background_image);
                        if (base64Image) {
                            await chrome.storage.local.set({
                                'backgroundImageData': base64Image
                            });
                            document.body.style.backgroundImage = `url('${base64Image}')`;
                        } else {
                            document.body.style.backgroundImage = 'url("background.jpg")';
                        }
                    } catch (error) {
                        console.error('Error processing background image:', error);
                        document.body.style.backgroundImage = 'url("background.jpg")';
                    }
                } else {
                    document.body.style.backgroundImage = 'url("background.jpg")';
                }
            } else {
                throw new Error('Invalid response format');
            }
        } catch (error) {
            console.error('Error:', error);
            document.getElementById('verse-text').textContent = 'Error loading verse. Please try again later.';
            document.getElementById('verse-reference').textContent = '';
            document.body.style.backgroundImage = 'url("background.jpg")';

            // Hide loading message even on error
            hideLoadingMessage();
        }
    } else {
        // We have both verse data and background image in storage
        document.getElementById('verse-text').textContent = storageResult.verseData.text;
        document.getElementById('verse-reference').textContent = storageResult.verseData.reference;
        document.body.style.backgroundImage = `url('${storageResult.backgroundImageData}')`;

        // Hide loading message
        hideLoadingMessage();
    }
}
// Get current date in YYYY-MM-DD format based on user's local time zone
const today = new Date();
// Use local date methods to get year, month, and day in user's time zone
const year = today.getFullYear();
const month = String(today.getMonth() + 1).padStart(2, '0'); // getMonth() is 0-indexed
const day = String(today.getDate()).padStart(2, '0');
// Format as YYYY-MM-DD
dateString = `${year}-${month}-${day}`;

// Fetch initial verse
fetchAndDisplayVerse(dateString);