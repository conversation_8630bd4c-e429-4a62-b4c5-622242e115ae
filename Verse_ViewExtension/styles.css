body {
    margin: 0;
    padding: 0;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    /* background-image: url('background.jpg'); */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    font-family: 'Georgia', serif;
    opacity: 1;
}

.container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    /* background-color: rgba(0, 0, 0, 0.5); */
}

.verse-container {
    max-width: 800px;
    padding: 2rem;
    text-align: center;
    color: white;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.verse-text {
    font-size: 2rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    font-style: italic;
}

.verse-reference {
    font-size: 1.2rem;
    font-weight: bold;
}

.date-picker {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    z-index: 1000;
}

.date-picker::-webkit-calendar-picker-indicator {
    filter: invert(1);
}

.support-link {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.support-link a {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    padding: 8px 12px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.support-link a:hover {
    color: #333;
    background-color: rgba(255, 255, 255, 0.9);
    text-decoration: underline;
}

.loading-message {
    max-width: 800px;
    padding: 2rem;
    text-align: center;
    color: white;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    position: absolute;
    z-index: 100;
}

.loading-message p {
    font-size: 1.5rem;
    font-style: italic;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}