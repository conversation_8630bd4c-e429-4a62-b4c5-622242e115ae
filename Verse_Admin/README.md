# Verse Admin

A simple tool to manage verse data in Firebase for the Verse View application.

## Setup

1. Create a Firebase project at [https://console.firebase.google.com/](https://console.firebase.google.com/)
2. Set up Firestore Database in your Firebase project
3. Generate a private key for your Firebase project:
   - Go to Project Settings > Service Accounts
   - Click "Generate new private key"
   - Save the JSON file securely
4. Copy `.env.example` to `.env` and fill in the values from your Firebase private key JSON file
5. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

## Usage

### Adding Verses

To add a single verse:

```
python add_verse.py --book "John" --chapter 3 --verse 16 --text "For God so loved the world that he gave his one and only Son, that whoever believes in him shall not perish but have eternal life."
```

To import verses from a CSV file:

```
python import_verses.py --file verses.csv
```

### Assigning <PERSON><PERSON><PERSON> to <PERSON>

To assign a verse to a specific day:

```
python assign_verse.py --day 25 --month 12 --verse_id "John_3_16"
```

To generate assignments for a whole year:

```
python generate_year.py --year 2023
```

## File Formats

### CSV Import Format

The CSV file for importing verses should have the following columns:
- book: The book of the Bible (e.g., "John")
- chapter: The chapter number (e.g., 3)
- verse: The verse number (e.g., 16)
- text: The verse text

Example:
```
book,chapter,verse,text
John,3,16,"For God so loved the world that he gave his one and only Son, that whoever believes in him shall not perish but have eternal life."
Romans,8,28,"And we know that in all things God works for the good of those who love him, who have been called according to his purpose."
```
