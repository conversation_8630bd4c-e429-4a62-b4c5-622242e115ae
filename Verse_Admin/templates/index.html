{% extends "base.html" %}

{% block title %}Verse Calendar - Verse Admin{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-md-6">
        <h1>Verse Calendar</h1>
        <p class="text-muted">Year: {{ current_year }}</p>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group">
            <a href="?year=default" class="btn btn-outline-primary {% if current_year == 'default' %}active{% endif %}">Default</a>
            <a href="?year=2023" class="btn btn-outline-primary {% if current_year == '2023' %}active{% endif %}">2023</a>
            <a href="?year=2024" class="btn btn-outline-primary {% if current_year == '2024' %}active{% endif %}">2024</a>
        </div>
    </div>
</div>

{% for month_num, month_data in calendar_data.items() %}
<div class="month-section">
    <h3 class="text-primary">{{ month_data.name }}</h3>
    <div class="table-responsive">
        <table class="table table-bordered table-sm">
            <thead>
                <tr class="day-header">
                    <th style="width: 50px;">Day</th>
                    <th style="width: 150px;">Reference</th>
                    <th>Text (First Version)</th>
                    <th style="width: 120px;">Image</th>
                    <th style="width: 80px;">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for day_num, day_data in month_data.days.items() %}
                <tr>
                    <td class="day-header">{{ day_num }}</td>
                    {% if day_data %}
                    <td class="verse-reference">
                        {{ day_data.book }} {{ day_data.chapter }}:{{ day_data.verseStart }}{% if day_data.verseEnd != day_data.verseStart %}-{{ day_data.verseEnd }}{% endif %}
                    </td>
                    <td class="verse-cell">
                        {% if day_data.versions %}
                            {% for version_id, version_data in day_data.versions.items() %}
                                <div class="verse-text">
                                    <strong>{{ version_data.translation }}:</strong> 
                                    {{ version_data.text[:100] }}{% if version_data.text|length > 100 %}...{% endif %}
                                </div>
                                {% break %}  {# Show only first version in table #}
                            {% endfor %}
                        {% else %}
                            <span class="text-muted">No text available</span>
                        {% endif %}
                    </td>
                    <td class="image-name">
                        {{ day_data.imageName or 'No image' }}
                    </td>
                    <td>
                        <a href="{{ url_for('verse_detail', verse_id=day_data.verse_id) }}" class="btn btn-sm btn-outline-info edit-btn">View</a>
                        <a href="{{ url_for('edit_verse', verse_id=day_data.verse_id) }}" class="btn btn-sm btn-outline-warning edit-btn">Edit</a>
                    </td>
                    {% else %}
                    <td colspan="4" class="text-muted text-center">No verse assigned</td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endfor %}
{% endblock %}
