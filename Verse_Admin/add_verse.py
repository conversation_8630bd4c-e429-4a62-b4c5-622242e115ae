#!/usr/bin/env python3
import argparse
from firebase_config import get_db

def add_verse(book, chapter, verse, text):
    """
    Add a verse to the Firebase database
    
    Args:
        book (str): Book name (e.g., "<PERSON>")
        chapter (int): Chapter number
        verse (int): Verse number
        text (str): Verse text
    
    Returns:
        str: Verse ID
    """
    # Format the verse ID
    verse_id = f"{book}_{chapter}_{verse}"
    
    # Create the verse document
    verse_data = {
        "book": book,
        "chapter": chapter,
        "verse": verse,
        "text": text,
        "reference": f"{book} {chapter}:{verse}"
    }
    
    # Get Firestore database
    db = get_db()
    
    # Add the verse to the verses collection
    verse_ref = db.collection('verses').document(verse_id)
    verse_ref.set(verse_data)
    
    print(f"Added verse: {verse_id}")
    return verse_id

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Add a verse to the Firebase database')
    parser.add_argument('--book', required=True, help='Book name (e.g., "<PERSON>")')
    parser.add_argument('--chapter', required=True, type=int, help='Chapter number')
    parser.add_argument('--verse', required=True, type=int, help='Verse number')
    parser.add_argument('--text', required=True, help='Verse text')
    
    args = parser.parse_args()
    
    # Add the verse
    add_verse(args.book, args.chapter, args.verse, args.text)
