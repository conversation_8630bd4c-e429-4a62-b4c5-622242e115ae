#!/usr/bin/env python3
import argparse
import random
from datetime import datetime, timedelta
from firebase_config import get_db
from assign_verse import assign_verse

def get_all_verse_ids():
    """
    Get all verse IDs from the database
    
    Returns:
        list: List of verse IDs
    """
    db = get_db()
    verses_ref = db.collection('verses')
    verses = verses_ref.stream()
    
    return [verse.id for verse in verses]

def generate_year_assignments(year):
    """
    Generate verse assignments for a whole year
    
    Args:
        year (int): Year to generate assignments for
    
    Returns:
        int: Number of days assigned
    """
    # Get all verse IDs
    verse_ids = get_all_verse_ids()
    
    if not verse_ids:
        print("Error: No verses found in the database")
        return 0
    
    # Shuffle the verse IDs to randomize assignments
    random.shuffle(verse_ids)
    
    # Calculate the number of days in the year
    is_leap_year = (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0)
    days_in_year = 366 if is_leap_year else 365
    
    # Make sure we have enough verses
    if len(verse_ids) < days_in_year:
        print(f"Warning: Not enough verses ({len(verse_ids)}) for a whole year ({days_in_year} days)")
        print("Some verses will be used multiple times")
        
        # Repeat the verse IDs to cover the whole year
        while len(verse_ids) < days_in_year:
            verse_ids.extend(verse_ids)
    
    # Trim to the exact number of days
    verse_ids = verse_ids[:days_in_year]
    
    # Generate assignments for each day of the year
    count = 0
    start_date = datetime(year, 1, 1)
    
    for day_offset in range(days_in_year):
        current_date = start_date + timedelta(days=day_offset)
        day = current_date.day
        month = current_date.month
        
        # Assign a verse to this day
        verse_id = verse_ids[day_offset]
        success = assign_verse(day, month, verse_id)
        
        if success:
            count += 1
    
    print(f"Generated {count} assignments for {year}")
    return count

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Generate verse assignments for a whole year')
    parser.add_argument('--year', required=True, type=int, help='Year to generate assignments for')
    
    args = parser.parse_args()
    
    # Generate the assignments
    generate_year_assignments(args.year)
