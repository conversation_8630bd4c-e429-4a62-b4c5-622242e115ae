#!/usr/bin/env python3
from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_cors import CORS
import calendar
from datetime import datetime
from firebase_config import get_db

app = Flask(__name__)
CORS(app)

# Initialize Firebase
db = get_db()

def get_all_days_data(year="default"):
    """Get all days data with verse information"""
    days_ref = db.collection('days')
    days = days_ref.stream()
    
    days_data = {}
    
    for day_doc in days:
        day_id = day_doc.id  # DD-MM format
        day_data = day_doc.to_dict()
        
        if year in day_data:
            verse_id = day_data[year].get('verseId')
            if verse_id:
                # Get verse details
                verse_ref = db.collection('verses').document(verse_id)
                verse_doc = verse_ref.get()
                
                if verse_doc.exists:
                    verse_data = verse_doc.to_dict()
                    
                    # Get all versions
                    versions_ref = verse_ref.collection('versions')
                    versions = versions_ref.stream()
                    
                    verse_versions = {}
                    for version_doc in versions:
                        version_data = version_doc.to_dict()
                        verse_versions[version_doc.id] = {
                            'text': version_data.get('text', ''),
                            'translation': version_data.get('translation', '')
                        }
                    
                    days_data[day_id] = {
                        'verse_id': verse_id,
                        'book': verse_data.get('book', ''),
                        'chapter': verse_data.get('chapter', ''),
                        'verseStart': verse_data.get('verseStart', ''),
                        'verseEnd': verse_data.get('verseEnd', ''),
                        'imageName': verse_data.get('imageName', ''),
                        'versions': verse_versions
                    }
    
    return days_data

def get_verse_details(verse_id):
    """Get detailed information about a specific verse"""
    verse_ref = db.collection('verses').document(verse_id)
    verse_doc = verse_ref.get()
    
    if not verse_doc.exists:
        return None
    
    verse_data = verse_doc.to_dict()
    
    # Get all versions
    versions_ref = verse_ref.collection('versions')
    versions = versions_ref.stream()
    
    verse_versions = {}
    for version_doc in versions:
        version_data = version_doc.to_dict()
        verse_versions[version_doc.id] = {
            'text': version_data.get('text', ''),
            'translation': version_data.get('translation', '')
        }
    
    return {
        'verse_id': verse_id,
        'book': verse_data.get('book', ''),
        'chapter': verse_data.get('chapter', ''),
        'verseStart': verse_data.get('verseStart', ''),
        'verseEnd': verse_data.get('verseEnd', ''),
        'imageName': verse_data.get('imageName', ''),
        'versions': verse_versions
    }

@app.route('/')
def index():
    """Main page showing all days in a table"""
    year = request.args.get('year', 'default')
    days_data = get_all_days_data(year)
    
    # Create a calendar structure
    calendar_data = {}
    for month in range(1, 13):
        month_name = calendar.month_name[month]
        calendar_data[month] = {
            'name': month_name,
            'days': {}
        }
        
        # Get number of days in month
        if month in [1, 3, 5, 7, 8, 10, 12]:
            days_in_month = 31
        elif month in [4, 6, 9, 11]:
            days_in_month = 30
        else:
            days_in_month = 29  # February (including leap day)
        
        for day in range(1, days_in_month + 1):
            day_key = f"{day:02d}-{month:02d}"
            calendar_data[month]['days'][day] = days_data.get(day_key, {})
    
    return render_template('index.html', calendar_data=calendar_data, current_year=year)

@app.route('/verse/<verse_id>')
def verse_detail(verse_id):
    """Detailed view of a specific verse"""
    verse_data = get_verse_details(verse_id)
    if not verse_data:
        return "Verse not found", 404
    
    return render_template('verse_detail.html', verse=verse_data)

@app.route('/edit/<verse_id>')
def edit_verse(verse_id):
    """Edit form for a specific verse"""
    verse_data = get_verse_details(verse_id)
    if not verse_data:
        return "Verse not found", 404
    
    return render_template('edit_verse.html', verse=verse_data)

@app.route('/api/update_verse/<verse_id>', methods=['POST'])
def update_verse(verse_id):
    """API endpoint to update verse data"""
    try:
        data = request.json
        
        # Update main verse document
        verse_ref = db.collection('verses').document(verse_id)
        
        # Update basic verse info
        verse_updates = {}
        if 'imageName' in data:
            verse_updates['imageName'] = data['imageName']
        
        if verse_updates:
            verse_ref.update(verse_updates)
        
        # Update versions
        if 'versions' in data:
            for version_id, version_data in data['versions'].items():
                version_ref = verse_ref.collection('versions').document(version_id)
                version_ref.set({
                    'text': version_data.get('text', ''),
                    'translation': version_data.get('translation', version_id.upper())
                })
        
        return jsonify({'success': True, 'message': 'Verse updated successfully'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/add_version/<verse_id>', methods=['POST'])
def add_version(verse_id):
    """API endpoint to add a new version to a verse"""
    try:
        data = request.json
        version_id = data.get('version_id', '').lower()
        text = data.get('text', '')
        translation = data.get('translation', version_id.upper())
        
        if not version_id or not text:
            return jsonify({'success': False, 'message': 'Version ID and text are required'}), 400
        
        verse_ref = db.collection('verses').document(verse_id)
        version_ref = verse_ref.collection('versions').document(version_id)
        
        version_ref.set({
            'text': text,
            'translation': translation
        })
        
        return jsonify({'success': True, 'message': 'Version added successfully'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5002)
