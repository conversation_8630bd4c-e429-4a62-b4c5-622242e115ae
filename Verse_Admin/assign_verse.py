#!/usr/bin/env python3
import argparse
from firebase_config import get_db

def assign_verse(day, month, verse_id, year="default"):
    """
    Assign a verse to a specific day

    Args:
        day (int): Day of the month (1-31)
        month (int): Month (1-12)
        verse_id (str): Verse ID (e.g., "John_3_16")
        year (str): Year identifier (default: "default")

    Returns:
        bool: True if successful
    """
    # Format the day ID (DD-MM)
    day_id = f"{day:02d}-{month:02d}"

    # Get Firestore database
    db = get_db()

    # Check if the verse exists
    verse_ref = db.collection('verses').document(verse_id)
    verse_doc = verse_ref.get()

    if not verse_doc.exists:
        print(f"Error: Verse {verse_id} does not exist")
        return False

    # Create the day document with year mapping structure
    day_data = {
        year: {
            "verseId": verse_id
        }
    }

    # Add the day to the days collection (merge to preserve other years if they exist)
    day_ref = db.collection('days').document(day_id)
    day_ref.set(day_data, merge=True)

    print(f"Assigned verse {verse_id} to day {day_id} for year {year}")
    return True

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Assign a verse to a specific day')
    parser.add_argument('--day', required=True, type=int, help='Day of the month (1-31)')
    parser.add_argument('--month', required=True, type=int, help='Month (1-12)')
    parser.add_argument('--verse_id', required=True, help='Verse ID (e.g., "John_3_16")')
    parser.add_argument('--year', default="default", help='Year identifier (default: "default")')

    args = parser.parse_args()

    # Validate inputs
    if args.day < 1 or args.day > 31:
        print("Error: Day must be between 1 and 31")
        exit(1)

    if args.month < 1 or args.month > 12:
        print("Error: Month must be between 1 and 12")
        exit(1)

    # Assign the verse
    assign_verse(args.day, args.month, args.verse_id, args.year)
